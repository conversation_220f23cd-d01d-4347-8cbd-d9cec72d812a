using FirebaseAdmin;
using Google.Apis.Auth.OAuth2;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Npgsql;
using OdmoriBa.Application.Interfaces.Data;
using OdmoriBa.Infrastructure.Data;
using OdmoriBa.Infrastructure.Data.Interceptors;
using OdmoriBa.Infrastructure.Data.Seed;

namespace OdmoriBa.Infrastructure;

public static class Configuration
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<ISaveChangesInterceptor, AuditableEntityInterceptor>();
        services.AddScoped<ISaveChangesInterceptor, SoftDeleteInterceptor>();
        services.AddScoped<ISaveChangesInterceptor, DomainEventsInterceptor>();

        var dataSource = new NpgsqlDataSourceBuilder(configuration.GetConnectionString("Postgres"))
            .EnableDynamicJson()
            .Build();
        
        services.AddDbContext<AppDbContext>((sp, options) => options
            .UseNpgsql(dataSource)
            .UseSnakeCaseNamingConvention()
            .AddInterceptors(sp.GetServices<ISaveChangesInterceptor>())
            .UseSeeding((context, _) => AppDbContextSeed.Seed(context))
            .UseAsyncSeeding((context, _, cancellationToken) => AppDbContextSeed.SeedAsync(context, cancellationToken))
        );

        services.AddScoped<IAppDbContext>(provider => provider.GetRequiredService<AppDbContext>());

        FirebaseApp.Create(new AppOptions
        {
            Credential = GoogleCredential.FromJson(configuration["Firebase:AdminServiceAccount"])
        });
        
        return services;
    }

    public static IServiceCollection AddInfrastructureWeb(this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddScoped<ISaveChangesInterceptor, AuditableEntityInterceptor>();
        services.AddScoped<ISaveChangesInterceptor, SoftDeleteInterceptor>();
        services.AddScoped<ISaveChangesInterceptor, DomainEventsInterceptor>();

        var dataSource = new NpgsqlDataSourceBuilder(configuration.GetConnectionString("Postgres"))
            .EnableDynamicJson()
            .Build();
        
        services.AddDbContextFactory<AppDbContext>((sp, options) => options
            .UseNpgsql(dataSource)
            .UseSnakeCaseNamingConvention()
            .AddInterceptors(sp.GetServices<ISaveChangesInterceptor>())
            .UseSeeding((context, _) => AppDbContextSeed.Seed(context))
            .UseAsyncSeeding((context, _, cancellationToken) => AppDbContextSeed.SeedAsync(context, cancellationToken)),
            ServiceLifetime.Scoped
        );

        services.AddTransient<IAppDbContext, AppDbContext>(provider =>
            provider.GetRequiredService<IDbContextFactory<AppDbContext>>().CreateDbContext());
        
        FirebaseApp.Create(new AppOptions
        {
            Credential = GoogleCredential.FromJson(configuration["Firebase:AdminServiceAccount"])
        });

        return services;
    }
}