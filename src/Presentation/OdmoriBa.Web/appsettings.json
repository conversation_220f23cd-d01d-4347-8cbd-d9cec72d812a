{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "Microsoft.AspNetCore": "Warning", "System": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}]}, "ConnectionStrings": {"Postgres": "Host=localhost;Port=5432;Database=odmori;Username=********;Password=********", "AzureStorage": "DefaultEndpointsProtocol=https;AccountName=stodmoridev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"}, "AllowedHosts": "*", "AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "odmori.net", "TenantId": "adb04ad8-606f-4ae6-91d9-cdbf40c482d1", "ClientId": "ddd9b213-90cf-4975-813f-bb0102e31f84", "CallbackPath": "/signin-oidc", "SignedOutCallbackPath": "/signout"}, "AzureStorage": {"PublicContainerUrl": "https://stodmoridev.blob.core.windows.net/public"}}