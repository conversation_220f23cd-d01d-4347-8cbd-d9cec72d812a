using System.Security.Claims;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.EntityFrameworkCore;
using OdmoriBa.Application.Features.Customers.Commands;
using OdmoriBa.Application.Interfaces.Auth;
using OdmoriBa.Application.Interfaces.Data;

namespace OdmoriBa.Web.Services.Auth;

public sealed class AuthenticationEventHandler(
    ILogger<AuthenticationEventHandler> logger,
    IMediator mediator,
    IAppDbContext dbContext,
    IIdentityService identityService
)
{
    public async Task OnTokenValidated(TokenValidatedContext context)
    {
        try
        {
            var identityId = context.Principal?.FindFirstValue("sid");
            if (string.IsNullOrEmpty(identityId))
            {
                logger.LogWarning("No NameIdentifier claim found in token");
                return;
            }

            // Check if user already exists in our database
            var existingUser = await dbContext.Users
                .FirstOrDefaultAsync(u => u.IdentityId == identityId, CancellationToken.None);

            if (existingUser != null)
            {
                // User exists, add sys_id claim
                var identity = (ClaimsIdentity)context.Principal!.Identity!;
                identity.AddClaim(new Claim("sys_id", existingUser.Id.ToString()));
                logger.LogDebug("Added sys_id claim for existing user {UserId}", existingUser.Id);
                return;
            }

            // User doesn't exist, create new admin user
            var name = context.Principal?.FindFirstValue("name") ?? "Admin User";
            var firstName = name.Split(' ')[0];
            var lastName = name.Split(' ')[1];
            var email = context.Principal?.FindFirstValue("preferred_username");

            if (string.IsNullOrWhiteSpace(email))
            {
                logger.LogError("Email is not provided");
                context.Fail("Authentication failed");
                return;
            }

            var command = new CreateAdminCommand(identityId, firstName, lastName, email);
            var result = await mediator.Send(command);

            if (result.IsError)
            {
                logger.LogError("Failed to create admin user for {IdentityId}: {Error}", identityId, result.Error);
                context.Fail($"Failed to create user: {result.Error}");
                return;
            }

            // Add sys_id claim for the newly created user
            var userIdentity = (ClaimsIdentity)context.Principal!.Identity!;
            userIdentity.AddClaim(new Claim("sys_id", result.Value.Id.ToString()));


            logger.LogInformation("Created new admin user {UserId} for {IdentityId}", result.Value.Id, identityId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during token validation");
            context.Fail("Authentication failed");
        }
    }
}