.mud-toolbar {
    height: auto;
}

/* Traveler List Improvements */
.traveler-stats-card {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-left: 4px solid var(--mud-palette-primary);
}

.travel-party-group-card {
    transition: all 0.2s ease-in-out;
    border-left: 4px solid var(--mud-palette-primary);
}

.travel-party-group-card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.travel-party-group-card-compact {
    transition: all 0.2s ease-in-out;
    border-left: 3px solid var(--mud-palette-primary);
    background-color: var(--mud-palette-background-gray);
    border-radius: 4px;
}

.travel-party-group-card-compact:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    background-color: var(--mud-palette-action-hover);
}

.mud-data-grid .mud-table-container .mud-table-head .mud-table-row .mud-table-cell {
    font-weight: 600;
    background-color: var(--mud-palette-background-gray);
}

.mud-data-grid .mud-table-container .mud-table-body .mud-table-row:hover {
    background-color: var(--mud-palette-action-hover);
}

/* Clickable status filter chips */
.mud-chip[style*="cursor: pointer"]:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    transition: all 0.2s ease-in-out;
}

.mud-chip[style*="cursor: pointer"]:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .travel-party-group-card .mud-stack-row,
    .travel-party-group-card-compact .mud-stack-row {
        flex-direction: column !important;
        align-items: flex-start !important;
    }

    .travel-party-group-card .mud-stack-row > *:last-child,
    .travel-party-group-card-compact .mud-stack-row > *:last-child {
        margin-top: 0.5rem;
        align-self: stretch;
    }

    .travel-party-group-card-compact .mud-stack-row > *:first-child {
        flex-wrap: wrap;
    }
}