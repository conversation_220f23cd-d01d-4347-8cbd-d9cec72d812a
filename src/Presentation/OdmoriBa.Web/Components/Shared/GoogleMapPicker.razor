@inject IJSRuntime JSRuntime
@inject IConfiguration Configuration
@inject ILogger<GoogleMapPicker> Logger
@implements IAsyncDisposable

<MudStack Spacing="3">
    <MudStack Row="true" Spacing="2">
        <MudTextField @bind-Value="LatitudeText"
                      Label="Latitude"
                      Variant="Variant.Outlined"
                      Margin="Margin.Dense"
                      Adornment="Adornment.Start"
                      AdornmentIcon="@Icons.Material.Filled.LocationOn"
                      OnBlur="OnCoordinateChanged" />
        <MudTextField @bind-Value="LongitudeText"
                      Label="Longitude"
                      Variant="Variant.Outlined"
                      Margin="Margin.Dense"
                      Adornment="Adornment.Start"
                      AdornmentIcon="@Icons.Material.Filled.LocationOn"
                      OnBlur="OnCoordinateChanged" />
        <MudButton Variant="Variant.Outlined"
                   Color="Color.Secondary"
                   StartIcon="@Icons.Material.Filled.Clear"
                   OnClick="ClearLocation">
            Clear
        </MudButton>
    </MudStack>
    
    <MudPaper Elevation="2" Style="height: 400px; width: 100%;">
        <div id="@_mapElementId" style="height: 100%; width: 100%;"></div>
    </MudPaper>
    
    <MudText Typo="Typo.caption" Color="Color.Secondary">
        Click on the map or drag the marker to set the location. You can also enter coordinates manually.
    </MudText>
</MudStack>

@code {
    [Parameter] public double? Latitude { get; set; }
    [Parameter] public EventCallback<double?> LatitudeChanged { get; set; }
    
    [Parameter] public double? Longitude { get; set; }
    [Parameter] public EventCallback<double?> LongitudeChanged { get; set; }

    private readonly string _mapElementId = $"map-{Guid.NewGuid():N}";
    private DotNetObjectReference<GoogleMapPicker>? _dotNetRef;
    private bool _isMapInitialized = false;

    private string LatitudeText
    {
        get => Latitude?.ToString("F6") ?? "";
        set
        {
            if (double.TryParse(value, out var lat))
            {
                Latitude = lat;
                LatitudeChanged.InvokeAsync(lat);
            }
            else if (string.IsNullOrWhiteSpace(value))
            {
                Latitude = null;
                LatitudeChanged.InvokeAsync(null);
            }
        }
    }

    private string LongitudeText
    {
        get => Longitude?.ToString("F6") ?? "";
        set
        {
            if (double.TryParse(value, out var lng))
            {
                Longitude = lng;
                LongitudeChanged.InvokeAsync(lng);
            }
            else if (string.IsNullOrWhiteSpace(value))
            {
                Longitude = null;
                LongitudeChanged.InvokeAsync(null);
            }
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await InitializeMap();
        }
    }

    private async Task InitializeMap()
    {
        try
        {
            var apiKey = Configuration["Google:MapsKey"];
            if (string.IsNullOrEmpty(apiKey))
            {
                Logger.LogWarning("Google Maps API key not found in configuration");
                return;
            }

            _dotNetRef = DotNetObjectReference.Create(this);
            
            // Load Google Maps API
            await JSRuntime.InvokeVoidAsync("loadGoogleMapsApi", apiKey);
            
            // Initialize map
            var lat = Latitude ?? 44.2619; // Default to Bosnia center
            var lng = Longitude ?? 17.2678;
            
            await JSRuntime.InvokeVoidAsync("GoogleMapsHelper.initializeMap", _mapElementId, _dotNetRef, apiKey, lat, lng);
            _isMapInitialized = true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error initializing Google Maps");
        }
    }

    [JSInvokable]
    public async Task OnLocationChanged(double latitude, double longitude)
    {
        Latitude = latitude;
        Longitude = longitude;

        await LatitudeChanged.InvokeAsync(latitude);
        await LongitudeChanged.InvokeAsync(longitude);

        StateHasChanged();
    }



    private async Task OnCoordinateChanged()
    {
        if (_isMapInitialized && Latitude.HasValue && Longitude.HasValue)
        {
            await JSRuntime.InvokeVoidAsync("GoogleMapsHelper.setMarkerPosition", Latitude.Value, Longitude.Value);
        }
    }

    private async Task ClearLocation()
    {
        Latitude = null;
        Longitude = null;
        
        await LatitudeChanged.InvokeAsync(null);
        await LongitudeChanged.InvokeAsync(null);
        
        if (_isMapInitialized)
        {
            await JSRuntime.InvokeVoidAsync("GoogleMapsHelper.clearMarker");
        }
        
        StateHasChanged();
    }

    public async ValueTask DisposeAsync()
    {
        if (_isMapInitialized)
        {
            try
            {
                await JSRuntime.InvokeVoidAsync("GoogleMapsHelper.dispose");
            }
            catch
            {
                // Ignore disposal errors
            }
        }
        
        _dotNetRef?.Dispose();
    }
}
