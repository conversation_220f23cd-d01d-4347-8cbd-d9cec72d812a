@page "/trips/{TripId:guid}/travelers"
@using OdmoriBa.Application.Common.Extensions
@using OdmoriBa.Application.Features.Customers.Models
@using OdmoriBa.Application.Features.Travelers.Commands
@using OdmoriBa.Application.Features.Travelers.Models
@using OdmoriBa.Application.Features.Trips.Models
@using OdmoriBa.Core.Domains.Travelers.Entities
@using OdmoriBa.Core.Domains.Trips.Entities
@using OdmoriBa.Core.Domains.Trips.Extensions
@using OdmoriBa.Resources.Extensions
@using OdmoriBa.Web.Components.Pages.Persons

@implements IDisposable

@inject IDialogService DialogService
@inject TripState TripState
@inject IMediator Mediator
@inject ISnackbar Snackbar

<PageTitle>Putovanje | Putnici</PageTitle>

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Putovanja", href: "/trips", icon: Icons.Material.Filled.CardTravel),
                   new BreadcrumbItem($"{_trip?.Title} ({_trip?.DateRange})", href: $"/trips/{_trip?.Id!}", icon: _trip?.TransportationType == TransportationType.Bus ? Icons.Material.Filled.DirectionsBus : Icons.Material.Filled.AirplanemodeActive),
                   new BreadcrumbItem("Putnici", href: $"/trips/{_trip?.Id!}/travelers", icon: Icons.Material.Filled.People),
               ])">
    </MudBreadcrumbs>
    <MudSpacer/>

    @if (_trip?.Type == TripType.OneDestination)
    {
        <MudButton StartIcon="@Icons.Material.Filled.Add"
                   OnClick="@(_ => CreateTravelPartyAsync(_trip.TripDestinations.FirstOrDefault()))"
                   Variant="Variant.Filled"
                   Color="Color.Primary">
            Dodaj putnike
        </MudButton>
    }
    @if (_trip?.Type == TripType.MultipleDestinations)
    {
        <MudMenu Label="Dodaj putnike" StartIcon="@Icons.Material.Filled.Add" Color="Color.Primary" Size="Size.Large"
                 Variant="Variant.Filled">
            @foreach (var tripDestination in _trip.TripDestinations)
            {
                <MudMenuItem Label="@tripDestination.Destination?.Name"
                             OnClick="@(_ => CreateTravelPartyAsync(tripDestination))"/>
            }
        </MudMenu>
    }
</MudToolBar>

<!-- Traveler Statistics Card -->
<MudCard Class="mb-4 traveler-stats-card" Elevation="2">
    <MudCardContent>
        <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
            <MudStack Row Spacing="4" AlignItems="AlignItems.Center">
                <MudText Typo="Typo.h6" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.People" Class="mr-2"/>
                    Putnici
                </MudText>
                <MudChip T="string" Color="Color.Info" Size="Size.Medium" Variant="Variant.Filled">
                    Prikazano: @_filteredTravelers.Count() / @_travelers.Count
                </MudChip>
            </MudStack>
        </MudStack>
    </MudCardContent>
</MudCard>

<MudDataGrid T="TravelerDto" Dense="true" Items="@_travelers"
             QuickFilter="QuickFilter" SortMode="SortMode.Single" ShowColumnOptions="false"
             Bordered="true" Groupable="true" GroupExpanded="true" GroupClass="mud-theme-info">
    <ToolBarContent>
        <MudStack Spacing="3" Class="w-100">
            <!-- First Row: Search and Destination Filters -->
            <MudStack Row Spacing="3" Wrap="Wrap.Wrap" AlignItems="AlignItems.Center">
                <MudTextField Class="pt-1" Variant="Variant.Outlined" Margin="Margin.Dense" T="string?"
                              @bind-Value="_filterSearch"
                              Placeholder="Pretraži po imenu, telefonu..."
                              Adornment="Adornment.Start" Immediate="true"
                              AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium"
                              Style="min-width: 250px;"/>

                @if (_trip?.Type == TripType.MultipleDestinations)
                {
                    <MudRadioGroup T="Guid?" @bind-Value="_filterTripDestination" Class="d-flex flex-row">
                        <MudRadio T="Guid?" Value="null" Color="Color.Secondary" Dense="true">Svi</MudRadio>
                        @foreach (var tripDestination in _trip?.TripDestinations ?? [])
                        {
                            <MudRadio T="Guid?" Value="@tripDestination.Id"
                                      Color="Color.Primary" Dense="true">@tripDestination.Destination?.Name</MudRadio>
                        }
                    </MudRadioGroup>
                }
            </MudStack>

            <!-- Second Row: Status Filter Chips -->
            <MudStack Row Spacing="2" Wrap="Wrap.Wrap" AlignItems="AlignItems.Center">
                <MudText Typo="Typo.body2" Class="mud-text-secondary">Filtriraj po statusu:</MudText>

                <MudChip T="string"
                         Color="Color.Success"
                         Size="Size.Small"
                         Icon="@Icons.Material.Filled.CheckCircle"
                         Variant="@(IsStatusSelected(TravelerStatus.Confirmed) ? Variant.Filled : Variant.Outlined)"
                         OnClick="@(() => ToggleStatusFilter(TravelerStatus.Confirmed))"
                         Style="cursor: pointer;">
                    Potvrđeno: @_filteredTravelers.Count(t => t.Status == TravelerStatus.Confirmed)
                </MudChip>

                <MudChip T="string"
                         Color="Color.Warning"
                         Size="Size.Small"
                         Icon="@Icons.Material.Filled.Schedule"
                         Variant="@(IsStatusSelected(TravelerStatus.Draft) ? Variant.Filled : Variant.Outlined)"
                         OnClick="@(() => ToggleStatusFilter(TravelerStatus.Draft))"
                         Style="cursor: pointer;">
                    U obradi: @_filteredTravelers.Count(t => t.Status == TravelerStatus.Draft)
                </MudChip>

                <MudChip T="string"
                         Color="Color.Info"
                         Size="Size.Small"
                         Icon="@Icons.Material.Filled.HourglassEmpty"
                         Variant="@(IsStatusSelected(TravelerStatus.Requested) ? Variant.Filled : Variant.Outlined)"
                         OnClick="@(() => ToggleStatusFilter(TravelerStatus.Requested))"
                         Style="cursor: pointer;">
                    Zahtjevi: @_filteredTravelers.Count(t => t.Status == TravelerStatus.Requested)
                </MudChip>

                <MudChip T="string"
                         Color="Color.Error"
                         Size="Size.Small"
                         Icon="@Icons.Material.Filled.Cancel"
                         Variant="@(IsStatusSelected(TravelerStatus.Cancelled) ? Variant.Filled : Variant.Outlined)"
                         OnClick="@(() => ToggleStatusFilter(TravelerStatus.Cancelled))"
                         Style="cursor: pointer;">
                    Otkazano: @_filteredTravelers.Count(t => t.Status == TravelerStatus.Cancelled)
                </MudChip>

                @if (_filterStatus != null && _filterStatus.Any())
                {
                    <MudChip T="string"
                             Color="Color.Default"
                             Size="Size.Small"
                             Icon="@Icons.Material.Filled.Clear"
                             Variant="Variant.Text"
                             OnClick="ClearStatusFilter"
                             Style="cursor: pointer;">
                        Očisti filter
                    </MudChip>
                }
            </MudStack>
        </MudStack>
    </ToolBarContent>
    <Columns>
        <TemplateColumn Title="Osoba">
            <CellTemplate>
                <MudStack Row>
                    <div>
                        <MudText Typo="Typo.body1">
                            @context.Item.Person?.FullName
                            <MudIconButton Size="@Size.Small" Icon="@Icons.Material.Filled.Edit"
                                           OnClick="@(_ => SavePersonAsync(context.Item))"/>
                        </MudText>
                        <MudText
                            Typo="Typo.caption">@context.Item.Person?.City | @context.Item.Person?.BirthDate.ToString("d") | @context.Item.Person?.Phone</MudText>
                    </div>
                    @if (!string.IsNullOrEmpty(context.Item.Note))
                    {
                        <MudTooltip Text="@context.Item.Note">
                            <MudIcon Icon="@Icons.Material.Filled.Info"/>
                        </MudTooltip>
                    }
                </MudStack>
            </CellTemplate>
        </TemplateColumn>
        <TemplateColumn Title="Mjesto ulaska">
            <CellTemplate>
                @context.Item?.DeparturePoint?.Stop?.City?.Name (@context.Item?.DeparturePoint?.Stop?.Address)
            </CellTemplate>
        </TemplateColumn>
        <TemplateColumn Title="Mjesto povratka">
            <CellTemplate>
                @context.Item?.ReturnDeparturePoint?.Stop?.City?.Name (@context.Item?.ReturnDeparturePoint?.Stop?.Address)
            </CellTemplate>
        </TemplateColumn>
        <TemplateColumn Title="Autobus polazak">
            <CellTemplate>
                @if (context.Item?.DepartureTripBus != null && context.Item?.DepartureSeatNumber != null)
                {
                    <MudChip T="string" Size="Size.Small" Color="Color.Primary" Variant="Variant.Filled"
                             Icon="@Icons.Material.Filled.DirectionsBus">
                        @context.Item.DepartureTripBus.Name (@context.Item.DepartureSeatNumber)
                    </MudChip>
                }
                else
                {
                    <MudChip T="string" Size="Size.Small" Color="Color.Default" Variant="Variant.Outlined"
                             Icon="@Icons.Material.Outlined.EventSeat">
                        Nije dodijeljeno
                    </MudChip>
                }
            </CellTemplate>
        </TemplateColumn>
        <TemplateColumn Title="Autobus povratak">
            <CellTemplate>
                @if (context.Item?.ReturnTripBus != null && context.Item?.ReturnSeatNumber != null)
                {
                    <MudChip T="string" Size="Size.Small" Color="Color.Secondary" Variant="Variant.Filled"
                             Icon="@Icons.Material.Filled.DirectionsBus">
                        @context.Item.ReturnTripBus.Name (@context.Item.ReturnSeatNumber)
                    </MudChip>
                }
                else
                {
                    <MudChip T="string" Size="Size.Small" Color="Color.Default" Variant="Variant.Outlined"
                             Icon="@Icons.Material.Outlined.EventSeat">
                        Nije dodijeljeno
                    </MudChip>
                }
            </CellTemplate>
        </TemplateColumn>
        <PropertyColumn Property="x => x.Price" Title="Cijena"/>
        <PropertyColumn Property="x => x.Discount" Title="Popust"/>
        <PropertyColumn Property="x => x.InsurancePrice" Title="PZO" Sortable="false"/>
        <PropertyColumn Property="x => x.TaxPrice" Title="Taksa" Sortable="false"/>
        <PropertyColumn Property="x => x.Status" Title="Status">
            <CellTemplate>
                <MudMenu>
                    <ActivatorContent>
                        <MudChip T="string" Variant="Variant.Outlined"
                                 Icon="@context.Item.Status.GetIcon()"
                                 IconColor="@context.Item.Status.GetColor()"
                                 Color="@(context.Item.Status.GetColor())">
                            @context.Item?.Status.GetLocalized()
                        </MudChip>
                    </ActivatorContent>
                    <ChildContent>
                        <MudMenuItem Label="@TravelerStatus.Draft.GetLocalized()"
                                     Disabled="!context.Item.Status.CanTransitionTo(TravelerStatus.Draft)"
                                     Icon="@TravelerStatus.Draft.GetIcon()"
                                     IconColor="TravelerStatus.Draft.GetColor()"
                                     OnClick="@(() => UpdateStatus(context.Item.TravelPartyId, context.Item.Id, TravelerStatus.Draft))"/>
                        <MudMenuItem Label="@TravelerStatus.Confirmed.GetLocalized()"
                                     Disabled="!context.Item.Status.CanTransitionTo(TravelerStatus.Confirmed)"
                                     Icon="@TravelerStatus.Confirmed.GetIcon()"
                                     IconColor="TravelerStatus.Confirmed.GetColor()"
                                     OnClick="@(() => UpdateStatus(context.Item.TravelPartyId, context.Item.Id, TravelerStatus.Confirmed))"/>
                        <MudDivider/>
                        <MudMenuItem Label="@TravelerStatus.Cancelled.GetLocalized()"
                                     Disabled="!context.Item.Status.CanTransitionTo(TravelerStatus.Cancelled)"
                                     Icon="@TravelerStatus.Cancelled.GetIcon()"
                                     IconColor="TravelerStatus.Cancelled.GetColor()"
                                     OnClick="@(() => UpdateStatus(context.Item.TravelPartyId, context.Item.Id, TravelerStatus.Cancelled))"/>

                    </ChildContent>
                </MudMenu>
            </CellTemplate>
        </PropertyColumn>
        <TemplateColumn Hidden="true" Grouping GroupBy="@(g => g.TravelPartyId)">
            <GroupTemplate>
                @{
                    var travelParty = _travelParties.First(s => s.Id == (Guid)context.Grouping.Key!);
                    var debt = travelParty.TotalDue;
                    var price = travelParty.TotalPrice;
                    var travelerCount = context.Grouping.Count();
                }

                <MudCard Elevation="0" Class="mb-1 travel-party-group-card-compact">
                    <MudCardContent Class="pa-2">
                        <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Spacing="2">
                            <!-- Left side - Main info -->
                            <MudStack Row Spacing="2" AlignItems="AlignItems.Center" Class="flex-grow-1">
                                @if (_trip?.Type == TripType.MultipleDestinations)
                                {
                                    <MudChip T="string" Size="Size.Small" Color="Color.Tertiary" Variant="Variant.Filled">
                                        @travelParty.TripDestination!.Destination!.Name
                                    </MudChip>
                                }
                                <MudText Typo="Typo.body1" Class="font-weight-bold">
                                    @travelParty.MainContact?.FullNameAndCity
                                </MudText>
                                <MudChip T="string" Size="Size.Small" Color="Color.Info" Variant="Variant.Outlined">
                                    @travelerCount putnik@(travelerCount == 1 ? "" : "a")
                                </MudChip>
                                <MudText Typo="Typo.body2" Class="mud-text-secondary">
                                    Cijena: @price.ToString("F2")
                                </MudText>
                                <MudChip T="string" Variant="Variant.Filled"
                                         Color="@(debt > 0 ? Color.Warning : Color.Success)"
                                         Size="Size.Small"
                                         Icon="@(debt > 0 ? Icons.Material.Filled.Warning : Icons.Material.Filled.CheckCircle)">
                                    Dug: @debt.ToString("F2")
                                </MudChip>
                                @if (!string.IsNullOrEmpty(travelParty.Note))
                                {
                                    <MudTooltip Text="@travelParty.Note">
                                        <MudIcon Icon="@Icons.Material.Filled.Info" Color="Color.Info" Size="Size.Small"/>
                                    </MudTooltip>
                                }
                                @if (!string.IsNullOrEmpty(travelParty.RequestNote))
                                {
                                    <MudTooltip Text="@travelParty.RequestNote">
                                        <MudIcon Icon="@Icons.Material.Filled.Message" Color="Color.Warning" Size="Size.Small"/>
                                    </MudTooltip>
                                }
                            </MudStack>

                            <!-- Right side - Actions -->
                            <MudStack Row Spacing="1" AlignItems="AlignItems.Center">
                                <MudMenu Label="Uplate"
                                         StartIcon="@Icons.Material.Filled.Payment"
                                         Color="Color.Info"
                                         Size="Size.Small"
                                         Variant="Variant.Outlined">
                                    @foreach (var payment in travelParty.Payments)
                                    {
                                        <MudMenuItem
                                            Label="@($"{payment.Amount:F2} - {payment.Type!.GetDescription()} - {payment.PaidAt:d} - {payment.PaidByPerson?.FullName}")"
                                            OnClick="@(_ => SavePaymentAsync(travelParty, payment))"/>
                                    }
                                    <MudDivider/>
                                    <MudMenuItem Label="Dodaj uplatu"
                                                 Icon="@Icons.Material.Filled.Add"
                                                 OnClick="@(_ => SavePaymentAsync(travelParty))"/>
                                </MudMenu>

                                <MudButton StartIcon="@Icons.Material.Filled.PersonAdd"
                                           Size="Size.Small"
                                           OnClick="@(_ => AddTravelerToPartyAsync(travelParty))"
                                           Variant="Variant.Outlined"
                                           Color="Color.Primary">
                                    Dodaj putnika
                                </MudButton>

                                <MudButton StartIcon="@Icons.Material.Filled.Edit"
                                           Size="Size.Small"
                                           OnClick="@(_ => UpdateTravelPartyAsync(travelParty))"
                                           Variant="Variant.Filled"
                                           Color="Color.Primary">
                                    Uredi grupu
                                </MudButton>
                            </MudStack>
                        </MudStack>
                    </MudCardContent>
                </MudCard>
            </GroupTemplate>
        </TemplateColumn>
        <TemplateColumn>
            <CellTemplate>
                <MudButton Size="@Size.Small" Color="Color.Primary" Variant="Variant.Filled"
                           StartIcon="@Icons.Material.Outlined.Edit"
                           OnClick="@(_ => UpdateAsync(context.Item!))">Uredi
                </MudButton>
            </CellTemplate>
        </TemplateColumn>
    </Columns>
</MudDataGrid>

@code {
    [Parameter] public Guid TripId { get; set; }

    private TripDto? _trip = new();
    private List<TravelerDto> _travelers = [];
    private List<TravelPartyDto> _travelParties = [];
    private string? _filterSearch;
    private IEnumerable<TravelerStatus>? _filterStatus;
    private Guid? _filterTripDestination;
    private bool _loading;

    // Computed property for filtered travelers
    private IEnumerable<TravelerDto> _filteredTravelers => _travelers.Where(QuickFilter);

    // Status filter helper methods
    private bool IsStatusSelected(TravelerStatus status)
    {
        return _filterStatus?.Contains(status) == true;
    }

    private void ToggleStatusFilter(TravelerStatus status)
    {
        if (_filterStatus == null)
        {
            _filterStatus = new List<TravelerStatus> { status };
        }
        else
        {
            var statusList = _filterStatus.ToList();
            if (statusList.Contains(status))
            {
                statusList.Remove(status);
            }
            else
            {
                statusList.Add(status);
            }
            _filterStatus = statusList;
        }
        StateHasChanged();
    }

    private void ClearStatusFilter()
    {
        _filterStatus = null;
        StateHasChanged();
    }

    protected override async Task OnParametersSetAsync()
    {
        _trip = await TripState.LoadTripAsync(TripId);
        _travelParties = await TripState.LoadTravelPartiesAsync(TripId);
        _travelers = _travelParties.SelectMany(s => s.Travelers).ToList();
    }

    protected override void OnInitialized()
    {
        TripState.OnChange += StateHasChanged;
    }

    public void Dispose()
    {
        TripState.OnChange -= StateHasChanged;
    }

    private Func<TravelerDto, bool> QuickFilter => x
        => (string.IsNullOrWhiteSpace(_filterSearch) ||
            x.Person!.FullName.Contains(_filterSearch, StringComparison.OrdinalIgnoreCase) ||
            x.Person!.Phone!.Number.Contains(_filterSearch, StringComparison.OrdinalIgnoreCase)
        ) && (_filterStatus == null || !_filterStatus.Any() || _filterStatus.Contains(x.Status))
          && (!_filterTripDestination.HasValue || x.TripDestinationId == _filterTripDestination);

    private async Task AddTravelerToPartyAsync(TravelPartyDto travelParty)
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<CreateTravelerDialog>
        {
            { x => x.Trip, _trip },
            { x => x.TravelParty, travelParty }
        };

        var dialog = await DialogService.ShowAsync<CreateTravelerDialog>("Dodaj putnika", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            _travelers.Add((result.Data as TravelerDto)!);
        }
    }

    private async Task CreateTravelPartyAsync(TripDestinationDto? tripDestination = null)
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Large, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<CreateTravelPartyDialog>
        {
            { x => x.Trip, _trip },
            { x => x.TripDestination, tripDestination }
        };

        var dialog = await DialogService.ShowAsync<CreateTravelPartyDialog>("Dodaj putnike", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            var travelParty = (result.Data as TravelPartyDto)!;
            _travelParties.Add(travelParty);
            _travelers.AddRange(travelParty.Travelers);
        }
    }

    private async Task UpdateTravelPartyAsync(TravelPartyDto travelParty)
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Medium, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<UpdateTravelPartyDialog>
        {
            { x => x.Data, travelParty },
        };

        var dialog = await DialogService.ShowAsync<UpdateTravelPartyDialog>("Uredi grupu", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            var resultData = (result.Data as TravelPartyDto)!;
            _travelParties[_travelParties.IndexOf(travelParty)] = resultData;
        }
    }

    private async Task UpdateAsync(TravelerDto data)
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<UpdateTravelerDialog>
        {
            { x => x.Trip, _trip },
            { x => x.Data, data }
        };

        var dialog = await DialogService.ShowAsync<UpdateTravelerDialog>($"Uredi putnika: {data.Person?.FullName}", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            if (result.Data != null && result.Data!.Equals(data.Id)) // delete
            {
                _travelers.RemoveAt(_travelers.FindIndex(s => s.Id == data.Id));
            }
            else
            {
                _travelers[_travelers.FindIndex(s => s.Id == data.Id)] = (result.Data as TravelerDto)!;
            }
        }
    }

    private async Task SavePaymentAsync(TravelPartyDto travelParty, PaymentDto? data = null)
    {
        var isEdit = data is not null;

        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SavePaymentDialog>
        {
            { x => x.Data, data },
            { x => x.TravelParty, travelParty },
            { x => x.IsEdit, isEdit }
        };

        var dialog = await DialogService.ShowAsync<SavePaymentDialog>($"{(!isEdit ? "Dodaj" : "Uredi")} uplatu", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            if (!isEdit)
            {
                travelParty.Payments.Add((result.Data as PaymentDto)!);
            }
            else if (result.Data != null && result.Data!.Equals(data!.Id)) // delete
            {
                travelParty.Payments.RemoveAt(travelParty.Payments.FindIndex(s => s.Id == data.Id));
            }
            else
            {
                travelParty.Payments[travelParty.Payments.FindIndex(s => s.Id == data!.Id)] = (result.Data as PaymentDto)!;
            }
        }
    }

    public async Task UpdateStatus(Guid travelPartyId, Guid travelerId, TravelerStatus newStatus)
    {
        MarkupString message = newStatus switch
        {
            TravelerStatus.Draft => (MarkupString)@"
                                              Jeste li sigurni da želite vratiti putnika u obradu? 
                                              ",
            TravelerStatus.Confirmed => (MarkupString)@"
                                                  Jeste li sigurni da želite potvrditi putnika? 
                                                  <br /> Nakon ovoga putniku ce biti poslana notifikacija
                                                  ",
            TravelerStatus.Cancelled => (MarkupString)@"
                                                  Jeste li sigurni da želite otkazati ovo putnika? 
                                                  <br /> Nakon ovoga putniku ce biti poslana notifikacija
                                                  ",
            _ => throw new ArgumentOutOfRangeException(nameof(newStatus), newStatus, null)
        };

        bool? dialogResult = await DialogService.ShowMessageBox(
            "Potvrda",
            message,
            yesText: "Da, nastavi", cancelText: "Ne, odustani");

        if (dialogResult == true)
        {
            _loading = true;
            StateHasChanged();
            var result = await Mediator.Send(new UpdateTravelerStatusCommand(travelPartyId, travelerId, newStatus));

            result.Switch(() =>
            {
                _travelers.FirstOrDefault(s => s.Id == travelerId)!.Status = newStatus;
                Snackbar.Add($"Status je promijenjen u: {newStatus.GetLocalized()}", Severity.Success);
            }, error => { Snackbar.Add($"Error on update status: {error.Code}: {error.Description}", Severity.Error); });

            _loading = false;
        }
    }


    private async Task SavePersonAsync(TravelerDto traveler)
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SavePersonDialog> { { x => x.Data, traveler.Person } };

        var dialog = await DialogService.ShowAsync<SavePersonDialog>("Uredi osobu", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            var person = result.Data as PersonDto;
            traveler.Person = person;
        }
    }

}
