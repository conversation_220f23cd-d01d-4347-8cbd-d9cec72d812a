@page "/"
@using OdmoriBa.Application.Common.Extensions
@using OdmoriBa.Application.Features.Trips.Queries
@using OdmoriBa.Application.Features.Dashboard.Queries
@using OdmoriBa.Application.Features.Dashboard.Models
@using OdmoriBa.Application.Features.Trips.Models
@using OdmoriBa.Core.Domains.Trips.Entities
@using OdmoriBa.Core.Domains.Travelers.Entities

@inject IMediator Mediator
@inject ISnackbar Snackbar
@inject NavigationManager NavigationManager

<PageTitle>Dashboard | Odmori.Ba</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="mt-4">



    <!-- Main Content Grid -->
    <MudGrid Spacing="3">

        <!-- Left Column -->
        <MudItem xs="12" lg="8">

            <!-- Upcoming Published Trips -->
            <MudCard Elevation="2" Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                            <MudText Typo="Typo.h6">
                                <MudIcon Icon="@Icons.Material.Filled.CardTravel" Class="mr-2"/>
                                Nadolazeća putovanja
                            </MudText>
                            <MudButton Href="/trips" Variant="Variant.Text" Size="Size.Small"
                                      EndIcon="@Icons.Material.Filled.ArrowForward">
                                Vidi sve
                            </MudButton>
                        </MudStack>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    @if (_upcomingTrips?.Any() == true)
                    {
                        <MudTable Items="_upcomingTrips" Hover="true" Dense="true" Striped="true">
                            <HeaderContent>
                                <MudTh>Putovanje</MudTh>
                                <MudTh>Datum</MudTh>
                                <MudTh>Transport</MudTh>
                                <MudTh>Zahtjevi</MudTh>
                                <MudTh>U obradi</MudTh>
                                <MudTh>Potvrđeno</MudTh>
                                <MudTh>Ukupno</MudTh>
                                <MudTh>Akcije</MudTh>
                            </HeaderContent>
                            <RowTemplate>
                                <MudTd DataLabel="Putovanje">
                                    <MudStack>
                                        <MudText Typo="Typo.body2" Style="font-weight: 500;">@context.Title</MudText>
                                        <MudText Typo="Typo.caption" Color="Color.Default">@context.Destination</MudText>
                                    </MudStack>
                                </MudTd>
                                <MudTd DataLabel="Datum">
                                    <MudText Typo="Typo.body2">@context.DateRange</MudText>
                                </MudTd>
                                <MudTd DataLabel="Transport">
                                    <MudChip T="string" Size="Size.Small" Color="Color.Default">
                                        @context.TransportationType.GetDescription()
                                    </MudChip>
                                </MudTd>
                                <MudTd DataLabel="Zahtjevi">
                                    <MudChip T="string" Size="Size.Small" Color="Color.Warning">@context.RequestedCount</MudChip>
                                </MudTd>
                                <MudTd DataLabel="U obradi">
                                    <MudChip T="string" Size="Size.Small" Color="Color.Primary">@context.DraftCount</MudChip>
                                </MudTd>
                                <MudTd DataLabel="Potvrđeno">
                                    <MudChip T="string" Size="Size.Small" Color="Color.Success">@context.ConfirmedCount</MudChip>
                                </MudTd>
                                <MudTd DataLabel="Ukupno">
                                    <MudText Typo="Typo.body2" Style="font-weight: 500;">@context.ActiveReservations</MudText>
                                </MudTd>
                                <MudTd DataLabel="Akcije">
                                    <MudIconButton Icon="@Icons.Material.Filled.ArrowForward" Size="Size.Small"
                                                  Href="@($"/trips/{context.TripId}")"/>
                                </MudTd>
                            </RowTemplate>
                        </MudTable>
                    }
                    else
                    {
                        <MudAlert Severity="Severity.Info">Nema nadolazećih putovanja.</MudAlert>
                    }
                </MudCardContent>
            </MudCard>

            <!-- Requested Travelers -->
            <MudCard Elevation="2" Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                            <MudText Typo="Typo.h6">
                                <MudIcon Icon="@Icons.Material.Filled.HourglassEmpty" Class="mr-2"/>
                                Zahtjevi za putovanje
                            </MudText>
                            <MudButton Href="/trips" Variant="Variant.Text" Size="Size.Small"
                                      EndIcon="@Icons.Material.Filled.ArrowForward">
                                Vidi sve
                            </MudButton>
                        </MudStack>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    @if (_requestedTravelers?.Any() == true)
                    {
                        <MudTable Items="_requestedTravelers" Hover="true" Dense="true" Striped="true">
                            <HeaderContent>
                                <MudTh>Putnik</MudTh>
                                <MudTh>Putovanje</MudTh>
                                <MudTh>Datum</MudTh>
                                <MudTh>Cijena</MudTh>
                                <MudTh>Akcije</MudTh>
                            </HeaderContent>
                            <RowTemplate>
                                <MudTd DataLabel="Putnik">
                                    <MudStack>
                                        <MudText Typo="Typo.body2" Style="font-weight: 500;">@context.TravelerName</MudText>
                                        <MudText Typo="Typo.caption" Color="Color.Default">@context.TravelerEmail</MudText>
                                    </MudStack>
                                </MudTd>
                                <MudTd DataLabel="Putovanje">
                                    <MudStack>
                                        <MudText Typo="Typo.body2" Style="font-weight: 500;">@context.TripTitle</MudText>
                                        <MudText Typo="Typo.caption" Color="Color.Default">@context.TripDestination</MudText>
                                    </MudStack>
                                </MudTd>
                                <MudTd DataLabel="Datum">
                                    <MudText Typo="Typo.body2">@context.DateRange</MudText>
                                </MudTd>
                                <MudTd DataLabel="Cijena">
                                    <MudText Typo="Typo.body2">@context.TotalPrice.ToString("C")</MudText>
                                </MudTd>
                                <MudTd DataLabel="Akcije">
                                    <MudIconButton Icon="@Icons.Material.Filled.ArrowForward" Size="Size.Small"
                                                  Href="@($"/trips/{context.TripId}")"/>
                                </MudTd>
                            </RowTemplate>
                        </MudTable>
                    }
                    else
                    {
                        <MudAlert Severity="Severity.Info">Nema zahtjeva za putovanje.</MudAlert>
                    }
                </MudCardContent>
            </MudCard>

            <!-- Draft Travelers -->
            <MudCard Elevation="2" Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                            <MudText Typo="Typo.h6">
                                <MudIcon Icon="@Icons.Material.Filled.Edit" Class="mr-2"/>
                                Putnici u obradi
                            </MudText>
                            <MudButton Href="/trips" Variant="Variant.Text" Size="Size.Small"
                                      EndIcon="@Icons.Material.Filled.ArrowForward">
                                Vidi sve
                            </MudButton>
                        </MudStack>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    @if (_draftTravelers?.Any() == true)
                    {
                        <MudTable Items="_draftTravelers" Hover="true" Dense="true" Striped="true">
                            <HeaderContent>
                                <MudTh>Putnik</MudTh>
                                <MudTh>Putovanje</MudTh>
                                <MudTh>Datum</MudTh>
                                <MudTh>Cijena</MudTh>
                                <MudTh>Akcije</MudTh>
                            </HeaderContent>
                            <RowTemplate>
                                <MudTd DataLabel="Putnik">
                                    <MudStack>
                                        <MudText Typo="Typo.body2" Style="font-weight: 500;">@context.TravelerName</MudText>
                                        <MudText Typo="Typo.caption" Color="Color.Default">@context.TravelerEmail</MudText>
                                    </MudStack>
                                </MudTd>
                                <MudTd DataLabel="Putovanje">
                                    <MudStack>
                                        <MudText Typo="Typo.body2" Style="font-weight: 500;">@context.TripTitle</MudText>
                                        <MudText Typo="Typo.caption" Color="Color.Default">@context.TripDestination</MudText>
                                    </MudStack>
                                </MudTd>
                                <MudTd DataLabel="Datum">
                                    <MudText Typo="Typo.body2">@context.DateRange</MudText>
                                </MudTd>
                                <MudTd DataLabel="Cijena">
                                    <MudText Typo="Typo.body2">@context.TotalPrice.ToString("C")</MudText>
                                </MudTd>
                                <MudTd DataLabel="Akcije">
                                    <MudIconButton Icon="@Icons.Material.Filled.ArrowForward" Size="Size.Small"
                                                  Href="@($"/trips/{context.TripId}")"/>
                                </MudTd>
                            </RowTemplate>
                        </MudTable>
                    }
                    else
                    {
                        <MudAlert Severity="Severity.Info">Nema putnika u obradi.</MudAlert>
                    }
                </MudCardContent>
            </MudCard>

        </MudItem>

        <!-- Right Column -->
        <MudItem xs="12" lg="4">

            <!-- Calendar -->
            <MudCard Elevation="2" Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">
                            <MudIcon Icon="@Icons.Material.Filled.CalendarMonth" Class="mr-2"/>
                            Kalendar putovanja
                        </MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudCalendar DateRangeChanged="LoadTrips" ShowDay="false" ShowWeek="false" Items="_events" Style="height: 300px;">
                        <CellTemplate>
                            <MudPaper
                                Class="@(((CustomCalendarItem)context).Status == TripStatus.Draft ? "mud-theme-info" : "mud-theme-success")"
                                Style="text-align: center; padding: 4px;">
                                <MudText Typo="Typo.caption">@context.Text</MudText>
                                <MudIconButton Size="Size.Small" Icon="@Icons.Material.Filled.ArrowRight"
                                               Href="@($"/trips/{((CustomCalendarItem)context).TripId}")"
                                               Style="margin-top: 2px;"/>
                            </MudPaper>
                        </CellTemplate>
                    </MudCalendar>
                </MudCardContent>
            </MudCard>

            <!-- Draft Trips -->
            <MudCard Elevation="2" Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                            <MudText Typo="Typo.h6">
                                <MudIcon Icon="@Icons.Material.Filled.Edit" Class="mr-2"/>
                                Putovanja u pripremi
                            </MudText>
                            <MudButton Href="/trips" Variant="Variant.Text" Size="Size.Small"
                                      EndIcon="@Icons.Material.Filled.ArrowForward">
                                Vidi sve
                            </MudButton>
                        </MudStack>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    @if (_draftTrips?.Any() == true)
                    {
                        <MudList T="string" Dense="true">
                            @foreach (var trip in _draftTrips)
                            {
                                <MudListItem T="string" Icon="@trip.Status.GetIcon()"
                                            IconColor="@trip.Status.GetColor()"
                                            Href="@($"/trips/{trip.Id}")">
                                    <MudStack>
                                        <MudText Typo="Typo.body2" Style="font-weight: 500;">@trip.Title</MudText>
                                        <MudText Typo="Typo.caption" Color="Color.Default">
                                            @string.Join(", ", trip.Destinations ?? new List<string>())
                                        </MudText>
                                        @if (trip.StartDate.HasValue && trip.EndDate.HasValue)
                                        {
                                            <MudText Typo="Typo.caption" Color="Color.Default">
                                                @trip.StartDate.Value.ToString("dd.MM.") - @trip.EndDate.Value.ToString("dd.MM.")
                                            </MudText>
                                        }
                                    </MudStack>
                                </MudListItem>
                                <MudDivider/>
                            }
                        </MudList>
                    }
                    else
                    {
                        <MudAlert Severity="Severity.Info">Nema putovanja u pripremi.</MudAlert>
                    }
                </MudCardContent>
            </MudCard>



        </MudItem>

    </MudGrid>

</MudContainer>

@code {
    private List<UpcomingTripDto>? _upcomingTrips;
    private List<PendingTravelerDto>? _requestedTravelers;
    private List<PendingTravelerDto>? _draftTravelers;
    private List<TripListItemDto>? _draftTrips;
    private List<CustomCalendarItem> _events = [];

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
        await LoadCalendarData();
    }

    private async Task LoadDashboardData()
    {
        try
        {
            // Load upcoming trips
            var upcomingTripsResult = await Mediator.Send(new GetUpcomingTripsQuery(5));
            _upcomingTrips = upcomingTripsResult.Match(
                value => value.Items,
                error =>
                {
                    Snackbar.Add($"Error loading upcoming trips: {error.Code}: {error.Description}", Severity.Error);
                    return new List<UpcomingTripDto>();
                });

            // Load all pending travelers
            var pendingTravelersResult = await Mediator.Send(new GetPendingTravelersQuery(20));
            var allPendingTravelers = pendingTravelersResult.Match(
                value => value.Items,
                error =>
                {
                    Snackbar.Add($"Error loading pending travelers: {error.Code}: {error.Description}", Severity.Error);
                    return new List<PendingTravelerDto>();
                });

            // Split into requested and draft travelers
            _requestedTravelers = allPendingTravelers?.Where(t => t.Status == TravelerStatus.Requested).Take(10).ToList();
            _draftTravelers = allPendingTravelers?.Where(t => t.Status == TravelerStatus.Draft).Take(10).ToList();

            // Load draft trips
            var draftTripsResult = await Mediator.Send(new GetDraftTripsQuery(5));
            _draftTrips = draftTripsResult.Match(
                value => value.Items,
                error =>
                {
                    Snackbar.Add($"Error loading draft trips: {error.Code}: {error.Description}", Severity.Error);
                    return new List<TripListItemDto>();
                });

            StateHasChanged();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error loading dashboard data: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadCalendarData()
    {
        var today = DateTime.Now;
        var startDate = new DateTime(today.Year, today.Month, 1);
        var endDate = startDate.AddMonths(2).AddDays(-1);

        await LoadTrips(new DateRange(startDate, endDate));
    }

    private async Task LoadTrips(DateRange arg)
    {
        try
        {
            var result = await Mediator.Send(new GetTripCalendarQuery((DateOnly)arg.Start.ToDateOnly()!, (DateOnly)arg.End?.ToDateOnly()!));

            _events = result.Match(value => value.Items.Select(s =>
                new CustomCalendarItem
                {
                    Start = (DateTime)s.StartDate?.ToDateTime(TimeOnly.MinValue)!,
                    End = (DateTime)s.EndDate?.ToDateTime(TimeOnly.MinValue)!,
                    Text = s.Title!,
                    Status = s.Status!.Value,
                    TripId = s.Id
                }).ToList(), error =>
            {
                Snackbar.Add($"Error loading trips: {error.Code}: {error.Description}", Severity.Error);
                return [];
            });

            StateHasChanged();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error loading calendar trips: {ex.Message}", Severity.Error);
        }
    }

    public class CustomCalendarItem : CalendarItem
    {
        public TripStatus Status { get; set; }
        public Guid TripId { get; set; }
    }
}