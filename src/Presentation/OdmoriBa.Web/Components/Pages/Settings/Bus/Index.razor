@page "/settings/buses"
@using OdmoriBa.Application.Common.Extensions
@using OdmoriBa.Application.Features.Companies.Models
@using OdmoriBa.Application.Features.Transportations.Models
@using OdmoriBa.Application.Features.Transportations.Queries
@inject IMediator Mediator
@inject IDialogService DialogService
@inject ISnackbar Snackbar

<PageTitle>Autobusi</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Postavke", href: "/settings", icon: Icons.Material.Filled.Settings),
                   new BreadcrumbItem("Autobusi", href: "/settings/buses", icon: Icons.Material.Filled.DirectionsBus)
               ])">
    </MudBreadcrumbs>
    <MudSpacer/>
    <MudButton StartIcon="@Icons.Material.Filled.Add" OnClick="@(_ => SaveAsync())" Variant="Variant.Filled"
               Color="Color.Primary">
        Dodaj
    </MudButton>
</MudToolBar>

<MudDataGrid Items="@_elements" T="BusDto"
             Striped="true"
             Dense="true"
             QuickFilter="QuickFilter">
    <ToolBarContent>
        <MudStack Spacing="3" Row="true" Wrap="Wrap.Wrap">
            <MudTextField Class="pt-1" Variant="Variant.Outlined" Margin="Margin.Dense" @bind-Value="_searchTerm"
                          T="string" Placeholder="Search"
                          Clearable="true"
                          Adornment="Adornment.Start"
                          AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium"/>

            <MudAutocomplete Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true" T="CompanyLightDto?"
                             Label="Firma"
                             DebounceInterval="300"
                             SearchFunc="@SearchCompanies"
                             @bind-Value="_company"
                             ResetValueOnEmptyText="true"
                             Clearable="true"
                             ToStringFunc="@(c => c?.Name)"/>
        </MudStack>
    </ToolBarContent>
    <Columns>
        <PropertyColumn Property="x => x.Name" Title="Naziv"/>
        <PropertyColumn Property="x => x.Company" Title="Firma">
            <CellTemplate> @context.Item.Company!.Name </CellTemplate>
        </PropertyColumn>
        <PropertyColumn Property="x => x.Capacity" Title="Broj sjedišta"/>
        <PropertyColumn Property="x => x.Status" Title="Status">
            <CellTemplate> @context.Item.Status.GetDescription() </CellTemplate>
        </PropertyColumn>
        <TemplateColumn>
            <CellTemplate>
                <MudTooltip Text="Uredi">
                    <MudIconButton Icon="@Icons.Material.Filled.Edit"
                            OnClick="@(() => SaveAsync(context.Item))"/>
                </MudTooltip>
            </CellTemplate>
        </TemplateColumn>
    </Columns>
    <PagerContent>
        <MudDataGridPager T="BusDto"/>
    </PagerContent>
</MudDataGrid>

@code {

    private List<BusDto> _elements = [];
    private List<CompanyLightDto> _companies = [];
    private string? _searchTerm;
    private CompanyLightDto? _company;

    protected override async Task OnInitializedAsync()
    {
        var result = await Mediator.Send(new GetBusesQuery());

        _elements = result.Match(
            value =>
            {
                _companies = value.Items.Select(s => s.Company!).DistinctBy(c => c.Id).ToList();
                return value.Items;
            },
            error =>
            {
                Snackbar.Add($"Error loading buses: {error.Code}: {error.Description}", Severity.Error);
                return [];
            });
    }

    private async Task SaveAsync(BusDto? data = null)
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveBusDialog> { { x => x.Data, data } };

        var dialog = await DialogService.ShowAsync<SaveBusDialog>($"{(data is null ? "Kreiraj" : "Uredi")} stanicu", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            if (data is null)
            {
                _elements.Add((result.Data as BusDto)!);
            }
            else
            {
                _elements[_elements.FindIndex(s => s.Id == data.Id)] = (result.Data as BusDto)!;
            }
        }
    }

    private Task<IEnumerable<CompanyLightDto>> SearchCompanies(string searchTerm, CancellationToken cancellationToken)
    {
        var result = _companies.Where(s =>
            string.IsNullOrWhiteSpace(searchTerm)
            || s.Name.ToLower().Contains(searchTerm.ToLower())
        );
        return Task.FromResult(result);
    }

    private Func<BusDto, bool> QuickFilter => x =>
        (string.IsNullOrEmpty(_searchTerm) ||
         x.Name.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase))
        && (_company is null || x.CompanyId == _company.Id);

}