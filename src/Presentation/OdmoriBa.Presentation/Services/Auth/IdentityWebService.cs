using System.Security.Claims;
using Microsoft.AspNetCore.Http;
using OdmoriBa.Application.Common.Constants;
using OdmoriBa.Application.Interfaces.Auth;

namespace OdmoriBa.Presentation.Services.Auth;

public sealed class IdentityWebService(IHttpContextAccessor httpContextAccessor) : IIdentityService
{
    public string IdentityId => httpContextAccessor.HttpContext?.User.FindFirstValue(CustomClaimTypes.Sid)
                                ?? string.Empty;

    public Guid UserId => Guid.Parse(httpContextAccessor.HttpContext?.User.FindFirstValue(CustomClaimTypes.SysId)
                                     ?? Guid.Empty.ToString());
}