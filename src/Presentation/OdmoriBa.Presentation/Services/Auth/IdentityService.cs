using System.Security.Claims;
using Microsoft.AspNetCore.Http;
using Microsoft.IdentityModel.Tokens;
using OdmoriBa.Application.Common.Constants;
using OdmoriBa.Application.Interfaces.Auth;

namespace OdmoriBa.Presentation.Services.Auth;

public sealed class IdentityService(IHttpContextAccessor httpContextAccessor) : IIdentityService
{
    private readonly ClaimsPrincipal _claimsPrincipal = httpContextAccessor.HttpContext?.User ??
                                                        throw new SecurityTokenException("Invalid token");

    public ClaimsPrincipal CurrentUser
    {
        get => _claimsPrincipal;
        set { }
    }

    public event Action<ClaimsPrincipal>? UserChanged;

    public string IdentityId => _claimsPrincipal.FindFirstValue(CustomClaimTypes.IdentityId) ??
                                throw new SecurityTokenException("Invalid token");

    public Guid UserId => Guid.Parse(_claimsPrincipal.FindFirstValue(CustomClaimTypes.SysId) ??
                                     throw new SecurityTokenException("Invalid token"));

    public void Initialize(ClaimsPrincipal claimsPrincipal)
    {
        throw new NotImplementedException();
    }

    public Task<Guid> GetUserIdAsync()
    {
        throw new NotImplementedException();
    }
}